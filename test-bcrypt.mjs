#!/usr/bin/env node

import bcrypt from 'bcrypt';

async function testBcrypt() {
  const password = 'admin';
  
  console.log('Testing bcrypt functionality...');
  console.log('Password to hash:', password);
  
  // Hash the password
  const hash = await bcrypt.hash(password, 10);
  console.log('Generated hash:', hash);
  
  // Verify the password
  const isValid = await bcrypt.compare(password, hash);
  console.log('Verification result:', isValid);
  
  // Test with the existing hash from database
  const existingHash = '$2b$10$.RBW3iWqZZf/FKh6RFhsi.J/MXNQGtmsw2BDUU6Jzvc.EZSBRtwKC';
  console.log('\nTesting existing hash from database...');
  console.log('Existing hash:', existingHash);
  
  const isValidExisting = await bcrypt.compare(password, existingHash);
  console.log('Verification with existing hash:', isValidExisting);
  
  // Test with wrong password
  const wrongPassword = 'wrongpassword';
  const isValidWrong = await bcrypt.compare(wrongPassword, hash);
  console.log('Verification with wrong password:', isValidWrong);
}

testBcrypt().catch(console.error);
