import { Page, expect } from '@playwright/test';

/**
 * Helper functions for E2E tests
 */

/**
 * Wait for the page to be fully loaded
 */
export async function waitForPageLoad(page: Page) {
  await page.waitForLoadState('networkidle');
  await page.waitForLoadState('domcontentloaded');
}

/**
 * Sign in with Google (mock for testing)
 */
export async function signInWithGoogle(page: Page) {
  // Navigate to sign-in page
  await page.goto('/auth/signin');
  await waitForPageLoad(page);
  
  // Click the Google sign-in button
  await page.click('button:has-text("Continue with Google")');
  
  // In a real test environment, you would handle OAuth flow
  // For now, we'll assume the sign-in is successful and we're redirected
  await page.waitForURL('/dashboard');
  await waitForPageLoad(page);
}

/**
 * Sign out from the application
 */
export async function signOut(page: Page) {
  // Look for user menu or sign out button
  const userMenu = page.locator('[data-testid="user-menu"]').or(
    page.locator('button:has-text("Sign out")')
  );
  
  if (await userMenu.isVisible()) {
    await userMenu.click();
    
    // Look for sign out option
    const signOutButton = page.locator('button:has-text("Sign out")').or(
      page.locator('[data-testid="sign-out"]')
    );
    
    if (await signOutButton.isVisible()) {
      await signOutButton.click();
    }
  }
  
  // Wait for redirect to sign-in page
  await page.waitForURL('/auth/signin');
}

/**
 * Navigate to a specific page and wait for it to load
 */
export async function navigateToPage(page: Page, path: string) {
  await page.goto(path);
  await waitForPageLoad(page);
}

/**
 * Check if user is authenticated
 */
export async function isAuthenticated(page: Page): Promise<boolean> {
  try {
    // Check if we're on the dashboard or any authenticated page
    const currentUrl = page.url();
    return !currentUrl.includes('/auth/signin');
  } catch {
    return false;
  }
}

/**
 * Wait for a specific element to be visible
 */
export async function waitForElement(page: Page, selector: string, timeout = 10000) {
  await page.waitForSelector(selector, { state: 'visible', timeout });
}

/**
 * Take a screenshot with a descriptive name
 */
export async function takeScreenshot(page: Page, name: string) {
  await page.screenshot({ 
    path: `tests/reports/e2e-artifacts/screenshots/${name}-${Date.now()}.png`,
    fullPage: true 
  });
}

/**
 * Check if the sidebar navigation is working
 */
export async function testSidebarNavigation(page: Page) {
  // Test main navigation items
  const navItems = [
    { text: 'Dashboard', url: '/dashboard' },
    { text: 'Brand Deep Dive', url: '/brand-deep-dive' },
    { text: 'Marketing Dashboard', url: '/marketing-dashboard' },
    { text: 'Executive Summary', url: '/executive-summary' },
    { text: 'Budget', url: '/budget' },
    { text: 'AI Assistant', url: '/ai-assistant' }
  ];

  for (const item of navItems) {
    const navLink = page.locator(`nav a:has-text("${item.text}")`);
    if (await navLink.isVisible()) {
      await navLink.click();
      await page.waitForURL(`**${item.url}`);
      await waitForPageLoad(page);
      
      // Verify we're on the correct page
      expect(page.url()).toContain(item.url);
    }
  }
}

/**
 * Test responsive design by changing viewport
 */
export async function testResponsiveDesign(page: Page) {
  const viewports = [
    { width: 1920, height: 1080, name: 'Desktop' },
    { width: 768, height: 1024, name: 'Tablet' },
    { width: 375, height: 667, name: 'Mobile' }
  ];

  for (const viewport of viewports) {
    await page.setViewportSize({ width: viewport.width, height: viewport.height });
    await page.waitForTimeout(1000); // Allow time for responsive changes
    
    // Take screenshot for visual verification
    await takeScreenshot(page, `responsive-${viewport.name.toLowerCase()}`);
  }
}

/**
 * Check for console errors
 */
export async function checkConsoleErrors(page: Page): Promise<string[]> {
  const errors: string[] = [];
  
  page.on('console', (msg) => {
    if (msg.type() === 'error') {
      errors.push(msg.text());
    }
  });
  
  return errors;
}

/**
 * Mock authentication for testing
 */
export async function mockAuthentication(page: Page) {
  // Add authentication cookies or local storage
  await page.addInitScript(() => {
    // Mock session data
    window.localStorage.setItem('test-auth', 'true');
  });
}
