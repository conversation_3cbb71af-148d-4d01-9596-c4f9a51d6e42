<testsuites id="" name="" tests="8" failures="2" skipped="0" errors="0" time="21.412727999999998">
<testsuite name="auth.spec.ts" timestamp="2025-05-29T23:27:30.511Z" hostname="chromium" tests="8" failures="2" skipped="0" time="28.112" errors="0">
<testcase name="Authentication Flow › should redirect unauthenticated users to sign-in page" classname="auth.spec.ts" time="1.979">
</testcase>
<testcase name="Authentication Flow › should display sign-in page correctly" classname="auth.spec.ts" time="1.884">
<failure message="auth.spec.ts:33:7 should display sign-in page correctly" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:33:7 › Authentication Flow › should display sign-in page correctly ─────

    Error: expect.toBeVisible: Error: strict mode violation: locator('svg') resolved to 3 elements:
        1) <svg fill="currentColor" viewBox="0 0 24 24" class="h-8 w-8 text-white">…</svg> aka getByRole('img').first()
        2) <svg viewBox="0 0 24 24" class="w-5 h-5 mr-3">…</svg> aka getByRole('button', { name: 'Continue with Google' })
        3) <svg width="40" height="40" fill="none" viewBox="0 0 40 40" data-next-mark-loading="true">…</svg> aka getByRole('button', { name: 'Open Next.js Dev Tools' })

    Call log:
      - expect.toBeVisible with timeout 10000ms
      - waiting for locator('svg')


      49 |     
      50 |     // Check NOLK logo
    > 51 |     await expect(page.locator('svg')).toBeVisible();
         |                                       ^
      52 |   });
      53 |
      54 |   test('should handle sign-in button click', async ({ page }) => {
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:51:39

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/auth-Authentication-Flow-s-ad9a6-play-sign-in-page-correctly-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/auth-Authentication-Flow-s-ad9a6-play-sign-in-page-correctly-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/auth-Authentication-Flow-s-ad9a6-play-sign-in-page-correctly-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/auth-Authentication-Flow-s-ad9a6-play-sign-in-page-correctly-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/auth-Authentication-Flow-s-ad9a6-play-sign-in-page-correctly-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/auth-Authentication-Flow-s-ad9a6-play-sign-in-page-correctly-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Authentication Flow › should handle sign-in button click" classname="auth.spec.ts" time="11.942">
<failure message="auth.spec.ts:54:7 should handle sign-in button click" type="FAILURE">
<![CDATA[  [chromium] › auth.spec.ts:54:7 › Authentication Flow › should handle sign-in button click ────────

    Error: Timed out 10000ms waiting for expect(locator).toHaveClass(expected)

    Locator: locator('button:has-text("Continue with Google")')
    Expected pattern: /.*loading.*|.*disabled.*/
    Received: <element(s) not found>
    Call log:
      - expect.toHaveClass with timeout 10000ms
      - waiting for locator('button:has-text("Continue with Google")')
        - waiting for" https://accounts.google.com/v3/signin/identifier?opparams=%253F&dsh=S1646002883%3A1748561253095549&client_id=************-74c7r54r6rj8fnqo1rle651glosa9toc.apps.googleusercontent.com&code_challenge=ve…" navigation to finish...
        - navigated to "https://accounts.google.com/v3/signin/identifier?opparams=%253F&dsh=S1646002883%3A1748561253095549&client_id=************-74c7r54r6rj8fnqo1rle651glosa9toc.apps.googleusercontent.com&code_challenge=ve…"


      62 |     // In a real environment, this would redirect to Google OAuth
      63 |     // For testing, we'll check that the button responds to clicks
    > 64 |     await expect(googleButton).toHaveClass(/.*loading.*|.*disabled.*/);
         |                                ^
      65 |   });
      66 |
      67 |   test('should redirect authenticated users away from sign-in page', async ({ page }) => {
        at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:64:32

    attachment #1: screenshot (image/png) ──────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/auth-Authentication-Flow-should-handle-sign-in-button-click-chromium/test-failed-1.png
    ────────────────────────────────────────────────────────────────────────────────────────────────

    attachment #2: video (video/webm) ──────────────────────────────────────────────────────────────
    tests/reports/e2e-artifacts/auth-Authentication-Flow-should-handle-sign-in-button-click-chromium/video.webm
    ────────────────────────────────────────────────────────────────────────────────────────────────

    Error Context: ../reports/e2e-artifacts/auth-Authentication-Flow-should-handle-sign-in-button-click-chromium/error-context.md
]]>
</failure>
<system-out>
<![CDATA[
[[ATTACHMENT|e2e-artifacts/auth-Authentication-Flow-should-handle-sign-in-button-click-chromium/test-failed-1.png]]

[[ATTACHMENT|e2e-artifacts/auth-Authentication-Flow-should-handle-sign-in-button-click-chromium/video.webm]]

[[ATTACHMENT|e2e-artifacts/auth-Authentication-Flow-should-handle-sign-in-button-click-chromium/error-context.md]]
]]>
</system-out>
</testcase>
<testcase name="Authentication Flow › should redirect authenticated users away from sign-in page" classname="auth.spec.ts" time="3.207">
</testcase>
<testcase name="Authentication Flow › should handle authentication errors gracefully" classname="auth.spec.ts" time="1.875">
</testcase>
<testcase name="Authentication Flow › should handle callback URL parameter" classname="auth.spec.ts" time="1.864">
</testcase>
<testcase name="Authentication Flow › should be responsive on different screen sizes" classname="auth.spec.ts" time="3.508">
</testcase>
<testcase name="Authentication Flow › should not have console errors on sign-in page" classname="auth.spec.ts" time="1.853">
</testcase>
</testsuite>
</testsuites>