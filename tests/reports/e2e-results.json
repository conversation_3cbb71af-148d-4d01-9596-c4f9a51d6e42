{"config": {"configFile": "/Users/<USER>/Projects/NOLK/nolk-v4/playwright.config.ts", "rootDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "forbidOnly": false, "fullyParallel": true, "globalSetup": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/global-setup.ts", "globalTeardown": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/global-teardown.ts", "globalTimeout": 0, "grep": {}, "grepInvert": null, "maxFailures": 3, "metadata": {"actualWorkers": 8}, "preserveOutput": "always", "reporter": [["html", {"outputFolder": "tests/reports/e2e-html"}], ["json", {"outputFile": "tests/reports/e2e-results.json"}], ["junit", {"outputFile": "tests/reports/e2e-junit.xml"}], ["list", null]], "reportSlowTests": {"max": 5, "threshold": 300000}, "quiet": false, "projects": [{"outputDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "chromium", "name": "chromium", "testDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "firefox", "name": "firefox", "testDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "webkit", "name": "webkit", "testDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "Mobile Chrome", "name": "Mobile Chrome", "testDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}, {"outputDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts", "repeatEach": 1, "retries": 0, "metadata": {"actualWorkers": 8}, "id": "Mobile Safari", "name": "Mobile Safari", "testDir": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e", "testIgnore": [], "testMatch": ["**/*.@(spec|test).?(c|m)[jt]s?(x)"], "timeout": 30000}], "shard": null, "updateSnapshots": "missing", "updateSourceMethod": "patch", "version": "1.52.0", "workers": 8, "webServer": {"command": "npm run dev", "url": "http://localhost:6699", "reuseExistingServer": true, "timeout": 120000}}, "suites": [{"title": "auth.spec.ts", "file": "auth.spec.ts", "column": 0, "line": 0, "specs": [], "suites": [{"title": "Authentication Flow", "file": "auth.spec.ts", "line": 10, "column": 6, "specs": [{"title": "should redirect unauthenticated users to sign-in page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 0, "parallelIndex": 0, "status": "passed", "duration": 1979, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-29T23:27:30.751Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d748ac400d08b85935ef-4e5ced825649901e9c2a", "file": "auth.spec.ts", "line": 16, "column": 7}, {"title": "should display sign-in page correctly", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 1, "parallelIndex": 1, "status": "failed", "duration": 1884, "error": {"message": "Error: expect.toBeVisible: Error: strict mode violation: locator('svg') resolved to 3 elements:\n    1) <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" class=\"h-8 w-8 text-white\">…</svg> aka getByRole('img').first()\n    2) <svg viewBox=\"0 0 24 24\" class=\"w-5 h-5 mr-3\">…</svg> aka getByRole('button', { name: 'Continue with Google' })\n    3) <svg width=\"40\" height=\"40\" fill=\"none\" viewBox=\"0 0 40 40\" data-next-mark-loading=\"true\">…</svg> aka getByRole('button', { name: 'Open Next.js Dev Tools' })\n\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('svg')\u001b[22m\n", "stack": "Error: expect.toBeVisible: Error: strict mode violation: locator('svg') resolved to 3 elements:\n    1) <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" class=\"h-8 w-8 text-white\">…</svg> aka getByRole('img').first()\n    2) <svg viewBox=\"0 0 24 24\" class=\"w-5 h-5 mr-3\">…</svg> aka getByRole('button', { name: 'Continue with Google' })\n    3) <svg width=\"40\" height=\"40\" fill=\"none\" viewBox=\"0 0 40 40\" data-next-mark-loading=\"true\">…</svg> aka getByRole('button', { name: 'Open Next.js Dev Tools' })\n\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('svg')\u001b[22m\n\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:51:39", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts", "column": 39, "line": 51}, "snippet": "\u001b[0m \u001b[90m 49 |\u001b[39m     \n \u001b[90m 50 |\u001b[39m     \u001b[90m// Check NOLK logo\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'svg'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 52 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 53 |\u001b[39m\n \u001b[90m 54 |\u001b[39m   test(\u001b[32m'should handle sign-in button click'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts", "column": 39, "line": 51}, "message": "Error: expect.toBeVisible: Error: strict mode violation: locator('svg') resolved to 3 elements:\n    1) <svg fill=\"currentColor\" viewBox=\"0 0 24 24\" class=\"h-8 w-8 text-white\">…</svg> aka getByRole('img').first()\n    2) <svg viewBox=\"0 0 24 24\" class=\"w-5 h-5 mr-3\">…</svg> aka getByRole('button', { name: 'Continue with Google' })\n    3) <svg width=\"40\" height=\"40\" fill=\"none\" viewBox=\"0 0 40 40\" data-next-mark-loading=\"true\">…</svg> aka getByRole('button', { name: 'Open Next.js Dev Tools' })\n\nCall log:\n\u001b[2m  - expect.toBeVisible with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('svg')\u001b[22m\n\n\n\u001b[0m \u001b[90m 49 |\u001b[39m     \n \u001b[90m 50 |\u001b[39m     \u001b[90m// Check NOLK logo\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 51 |\u001b[39m     \u001b[36mawait\u001b[39m expect(page\u001b[33m.\u001b[39mlocator(\u001b[32m'svg'\u001b[39m))\u001b[33m.\u001b[39mtoBeVisible()\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                       \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 52 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 53 |\u001b[39m\n \u001b[90m 54 |\u001b[39m   test(\u001b[32m'should handle sign-in button click'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:51:39\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-29T23:27:30.749Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/auth-Authentication-Flow-s-ad9a6-play-sign-in-page-correctly-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/auth-Authentication-Flow-s-ad9a6-play-sign-in-page-correctly-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/auth-Authentication-Flow-s-ad9a6-play-sign-in-page-correctly-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts", "column": 39, "line": 51}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-77f492acb29e9c3cd8d1", "file": "auth.spec.ts", "line": 33, "column": 7}, {"title": "should handle sign-in button click", "ok": false, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 2, "parallelIndex": 2, "status": "failed", "duration": 11942, "error": {"message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('button:has-text(\"Continue with Google\")')\nExpected pattern: \u001b[32m/.*loading.*|.*disabled.*/\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toHaveClass with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('button:has-text(\"Continue with Google\")')\u001b[22m\n\u001b[2m    - waiting for\" https://accounts.google.com/v3/signin/identifier?opparams=%253F&dsh=S1646002883%3A1748561253095549&client_id=************-74c7r54r6rj8fnqo1rle651glosa9toc.apps.googleusercontent.com&code_challenge=ve…\" navigation to finish...\u001b[22m\n\u001b[2m    - navigated to \"https://accounts.google.com/v3/signin/identifier?opparams=%253F&dsh=S1646002883%3A1748561253095549&client_id=************-74c7r54r6rj8fnqo1rle651glosa9toc.apps.googleusercontent.com&code_challenge=ve…\"\u001b[22m\n", "stack": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('button:has-text(\"Continue with Google\")')\nExpected pattern: \u001b[32m/.*loading.*|.*disabled.*/\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toHaveClass with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('button:has-text(\"Continue with Google\")')\u001b[22m\n\u001b[2m    - waiting for\" https://accounts.google.com/v3/signin/identifier?opparams=%253F&dsh=S1646002883%3A1748561253095549&client_id=************-74c7r54r6rj8fnqo1rle651glosa9toc.apps.googleusercontent.com&code_challenge=ve…\" navigation to finish...\u001b[22m\n\u001b[2m    - navigated to \"https://accounts.google.com/v3/signin/identifier?opparams=%253F&dsh=S1646002883%3A1748561253095549&client_id=************-74c7r54r6rj8fnqo1rle651glosa9toc.apps.googleusercontent.com&code_challenge=ve…\"\u001b[22m\n\n    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:64:32", "location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts", "column": 32, "line": 64}, "snippet": "\u001b[0m \u001b[90m 62 |\u001b[39m     \u001b[90m// In a real environment, this would redirect to Google OAuth\u001b[39m\n \u001b[90m 63 |\u001b[39m     \u001b[90m// For testing, we'll check that the button responds to clicks\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 64 |\u001b[39m     \u001b[36mawait\u001b[39m expect(googleButton)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/.*loading.*|.*disabled.*/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 65 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 66 |\u001b[39m\n \u001b[90m 67 |\u001b[39m   test(\u001b[32m'should redirect authenticated users away from sign-in page'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m"}, "errors": [{"location": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts", "column": 32, "line": 64}, "message": "Error: \u001b[31mTimed out 10000ms waiting for \u001b[39m\u001b[2mexpect(\u001b[22m\u001b[31mlocator\u001b[39m\u001b[2m).\u001b[22mtoHaveClass\u001b[2m(\u001b[22m\u001b[32mexpected\u001b[39m\u001b[2m)\u001b[22m\n\nLocator: locator('button:has-text(\"Continue with Google\")')\nExpected pattern: \u001b[32m/.*loading.*|.*disabled.*/\u001b[39m\nReceived: <element(s) not found>\nCall log:\n\u001b[2m  - expect.toHaveClass with timeout 10000ms\u001b[22m\n\u001b[2m  - waiting for locator('button:has-text(\"Continue with Google\")')\u001b[22m\n\u001b[2m    - waiting for\" https://accounts.google.com/v3/signin/identifier?opparams=%253F&dsh=S1646002883%3A1748561253095549&client_id=************-74c7r54r6rj8fnqo1rle651glosa9toc.apps.googleusercontent.com&code_challenge=ve…\" navigation to finish...\u001b[22m\n\u001b[2m    - navigated to \"https://accounts.google.com/v3/signin/identifier?opparams=%253F&dsh=S1646002883%3A1748561253095549&client_id=************-74c7r54r6rj8fnqo1rle651glosa9toc.apps.googleusercontent.com&code_challenge=ve…\"\u001b[22m\n\n\n\u001b[0m \u001b[90m 62 |\u001b[39m     \u001b[90m// In a real environment, this would redirect to Google OAuth\u001b[39m\n \u001b[90m 63 |\u001b[39m     \u001b[90m// For testing, we'll check that the button responds to clicks\u001b[39m\n\u001b[31m\u001b[1m>\u001b[22m\u001b[39m\u001b[90m 64 |\u001b[39m     \u001b[36mawait\u001b[39m expect(googleButton)\u001b[33m.\u001b[39mtoHaveClass(\u001b[35m/.*loading.*|.*disabled.*/\u001b[39m)\u001b[33m;\u001b[39m\n \u001b[90m    |\u001b[39m                                \u001b[31m\u001b[1m^\u001b[22m\u001b[39m\n \u001b[90m 65 |\u001b[39m   })\u001b[33m;\u001b[39m\n \u001b[90m 66 |\u001b[39m\n \u001b[90m 67 |\u001b[39m   test(\u001b[32m'should redirect authenticated users away from sign-in page'\u001b[39m\u001b[33m,\u001b[39m \u001b[36masync\u001b[39m ({ page }) \u001b[33m=>\u001b[39m {\u001b[0m\n\u001b[2m    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:64:32\u001b[22m"}], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-29T23:27:30.746Z", "annotations": [], "attachments": [{"name": "screenshot", "contentType": "image/png", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/auth-Authentication-Flow-should-handle-sign-in-button-click-chromium/test-failed-1.png"}, {"name": "video", "contentType": "video/webm", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/auth-Authentication-Flow-should-handle-sign-in-button-click-chromium/video.webm"}, {"name": "_error-context-0", "contentType": "text/markdown", "path": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/reports/e2e-artifacts/auth-Authentication-Flow-should-handle-sign-in-button-click-chromium/error-context.md"}], "errorLocation": {"file": "/Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts", "column": 32, "line": 64}}], "status": "unexpected"}], "id": "d748ac400d08b85935ef-c58d232e80184ccab899", "file": "auth.spec.ts", "line": 54, "column": 7}, {"title": "should redirect authenticated users away from sign-in page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 3, "parallelIndex": 3, "status": "passed", "duration": 3207, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-29T23:27:30.751Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d748ac400d08b85935ef-149671d8f3734ea109dd", "file": "auth.spec.ts", "line": 67, "column": 7}, {"title": "should handle authentication errors gracefully", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 4, "parallelIndex": 4, "status": "passed", "duration": 1875, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-29T23:27:30.754Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d748ac400d08b85935ef-df21f2f4589a109b5de2", "file": "auth.spec.ts", "line": 87, "column": 7}, {"title": "should handle callback URL parameter", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 5, "parallelIndex": 5, "status": "passed", "duration": 1864, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-29T23:27:30.750Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d748ac400d08b85935ef-e0546c5298369166a43d", "file": "auth.spec.ts", "line": 99, "column": 7}, {"title": "should be responsive on different screen sizes", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 6, "parallelIndex": 6, "status": "passed", "duration": 3508, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-29T23:27:30.751Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d748ac400d08b85935ef-7a1b434d852c5515c544", "file": "auth.spec.ts", "line": 111, "column": 7}, {"title": "should not have console errors on sign-in page", "ok": true, "tags": [], "tests": [{"timeout": 30000, "annotations": [], "expectedStatus": "passed", "projectId": "chromium", "projectName": "chromium", "results": [{"workerIndex": 7, "parallelIndex": 7, "status": "passed", "duration": 1853, "errors": [], "stdout": [], "stderr": [], "retry": 0, "startTime": "2025-05-29T23:27:30.750Z", "annotations": [], "attachments": []}], "status": "expected"}], "id": "d748ac400d08b85935ef-deb3c74df68a538f9742", "file": "auth.spec.ts", "line": 144, "column": 7}]}]}], "errors": [], "stats": {"startTime": "2025-05-29T23:27:21.516Z", "duration": 21412.728, "expected": 6, "skipped": 0, "unexpected": 2, "flaky": 0}}