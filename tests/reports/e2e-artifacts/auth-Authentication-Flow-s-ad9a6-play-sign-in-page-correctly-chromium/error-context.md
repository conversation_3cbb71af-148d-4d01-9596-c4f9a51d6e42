# Test info

- Name: Authentication Flow >> should display sign-in page correctly
- Location: /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:33:7

# Error details

```
Error: expect.toBeVisible: Error: strict mode violation: locator('svg') resolved to 3 elements:
    1) <svg fill="currentColor" viewBox="0 0 24 24" class="h-8 w-8 text-white">…</svg> aka getByRole('img').first()
    2) <svg viewBox="0 0 24 24" class="w-5 h-5 mr-3">…</svg> aka getByRole('button', { name: 'Continue with Google' })
    3) <svg width="40" height="40" fill="none" viewBox="0 0 40 40" data-next-mark-loading="true">…</svg> aka getByRole('button', { name: 'Open Next.js Dev Tools' })

Call log:
  - expect.toBeVisible with timeout 10000ms
  - waiting for locator('svg')

    at /Users/<USER>/Projects/NOLK/nolk-v4/tests/e2e/auth.spec.ts:51:39
```

# Page snapshot

```yaml
- img
- heading "NOLK" [level=1]
- heading "Sign in to your account" [level=2]
- paragraph: Please sign in with your Google account to access the dashboard
- button "Continue with Google":
  - img
  - text: Continue with Google
- paragraph: By signing in, you agree to our terms of service and privacy policy.
- alert
- button "Open Next.js Dev Tools":
  - img
```

# Test source

```ts
   1 | import { test, expect } from '@playwright/test';
   2 | import { 
   3 |   waitForPageLoad, 
   4 |   signInWithGoogle, 
   5 |   signOut, 
   6 |   isAuthenticated,
   7 |   takeScreenshot 
   8 | } from './utils/test-helpers';
   9 |
   10 | test.describe('Authentication Flow', () => {
   11 |   test.beforeEach(async ({ page }) => {
   12 |     // Start each test from the home page
   13 |     await page.goto('/');
   14 |   });
   15 |
   16 |   test('should redirect unauthenticated users to sign-in page', async ({ page }) => {
   17 |     // When visiting the root page without authentication
   18 |     await page.goto('/');
   19 |     
   20 |     // Should be redirected to sign-in page
   21 |     await page.waitForURL('**/auth/signin');
   22 |     await waitForPageLoad(page);
   23 |     
   24 |     // Verify sign-in page elements
   25 |     await expect(page.locator('h1')).toContainText('NOLK');
   26 |     await expect(page.locator('h2')).toContainText('Sign in to your account');
   27 |     await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();
   28 |     
   29 |     // Take screenshot for verification
   30 |     await takeScreenshot(page, 'sign-in-page');
   31 |   });
   32 |
   33 |   test('should display sign-in page correctly', async ({ page }) => {
   34 |     await page.goto('/auth/signin');
   35 |     await waitForPageLoad(page);
   36 |     
   37 |     // Check page title
   38 |     await expect(page).toHaveTitle(/NOLK|Create Next App/);
   39 |     
   40 |     // Check main elements
   41 |     await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
   42 |     await expect(page.locator('h2:has-text("Sign in to your account")')).toBeVisible();
   43 |     await expect(page.locator('p:has-text("Please sign in with your Google account")')).toBeVisible();
   44 |     
   45 |     // Check Google sign-in button
   46 |     const googleButton = page.locator('button:has-text("Continue with Google")');
   47 |     await expect(googleButton).toBeVisible();
   48 |     await expect(googleButton).toBeEnabled();
   49 |     
   50 |     // Check NOLK logo
>  51 |     await expect(page.locator('svg')).toBeVisible();
      |                                       ^ Error: expect.toBeVisible: Error: strict mode violation: locator('svg') resolved to 3 elements:
   52 |   });
   53 |
   54 |   test('should handle sign-in button click', async ({ page }) => {
   55 |     await page.goto('/auth/signin');
   56 |     await waitForPageLoad(page);
   57 |     
   58 |     // Click the Google sign-in button
   59 |     const googleButton = page.locator('button:has-text("Continue with Google")');
   60 |     await googleButton.click();
   61 |     
   62 |     // In a real environment, this would redirect to Google OAuth
   63 |     // For testing, we'll check that the button responds to clicks
   64 |     await expect(googleButton).toHaveClass(/.*loading.*|.*disabled.*/);
   65 |   });
   66 |
   67 |   test('should redirect authenticated users away from sign-in page', async ({ page }) => {
   68 |     // Mock authentication
   69 |     await page.addInitScript(() => {
   70 |       // Mock NextAuth session
   71 |       window.localStorage.setItem('nextauth.session-token', 'mock-token');
   72 |     });
   73 |     
   74 |     await page.goto('/auth/signin');
   75 |     
   76 |     // Should be redirected away from sign-in page
   77 |     // Note: This test might need adjustment based on actual auth implementation
   78 |     await page.waitForTimeout(2000);
   79 |     
   80 |     // Check if we're redirected (URL should not contain signin)
   81 |     const currentUrl = page.url();
   82 |     if (!currentUrl.includes('/auth/signin')) {
   83 |       expect(currentUrl).toContain('/dashboard');
   84 |     }
   85 |   });
   86 |
   87 |   test('should handle authentication errors gracefully', async ({ page }) => {
   88 |     await page.goto('/auth/signin?error=OAuthSignin');
   89 |     await waitForPageLoad(page);
   90 |     
   91 |     // Should still show the sign-in page
   92 |     await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
   93 |     await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();
   94 |     
   95 |     // Could check for error message if implemented
   96 |     // await expect(page.locator('.error-message')).toBeVisible();
   97 |   });
   98 |
   99 |   test('should handle callback URL parameter', async ({ page }) => {
  100 |     const callbackUrl = encodeURIComponent('/brand-deep-dive');
  101 |     await page.goto(`/auth/signin?callbackUrl=${callbackUrl}`);
  102 |     await waitForPageLoad(page);
  103 |     
  104 |     // Should show sign-in page with callback URL preserved
  105 |     await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
  106 |     
  107 |     // URL should contain the callback parameter
  108 |     expect(page.url()).toContain(`callbackUrl=${callbackUrl}`);
  109 |   });
  110 |
  111 |   test('should be responsive on different screen sizes', async ({ page }) => {
  112 |     await page.goto('/auth/signin');
  113 |     await waitForPageLoad(page);
  114 |     
  115 |     // Test mobile viewport
  116 |     await page.setViewportSize({ width: 375, height: 667 });
  117 |     await page.waitForTimeout(500);
  118 |     
  119 |     // Elements should still be visible and properly arranged
  120 |     await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
  121 |     await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();
  122 |     
  123 |     await takeScreenshot(page, 'sign-in-mobile');
  124 |     
  125 |     // Test tablet viewport
  126 |     await page.setViewportSize({ width: 768, height: 1024 });
  127 |     await page.waitForTimeout(500);
  128 |     
  129 |     await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
  130 |     await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();
  131 |     
  132 |     await takeScreenshot(page, 'sign-in-tablet');
  133 |     
  134 |     // Test desktop viewport
  135 |     await page.setViewportSize({ width: 1920, height: 1080 });
  136 |     await page.waitForTimeout(500);
  137 |     
  138 |     await expect(page.locator('h1:has-text("NOLK")')).toBeVisible();
  139 |     await expect(page.locator('button:has-text("Continue with Google")')).toBeVisible();
  140 |     
  141 |     await takeScreenshot(page, 'sign-in-desktop');
  142 |   });
  143 |
  144 |   test('should not have console errors on sign-in page', async ({ page }) => {
  145 |     const consoleErrors: string[] = [];
  146 |     
  147 |     page.on('console', (msg) => {
  148 |       if (msg.type() === 'error') {
  149 |         consoleErrors.push(msg.text());
  150 |       }
  151 |     });
```